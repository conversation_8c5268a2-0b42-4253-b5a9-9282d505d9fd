# GitHub自动注册服务 - 部署指南

## 概述

本指南详细介绍了如何部署GitHub自动注册服务的完整SaaS解决方案，包括后端微服务、数据库、监控系统和客户端脚本的完整部署流程。

## 系统要求

### 硬件要求
```yaml
最小配置 (开发/测试环境):
  CPU: 2核心
  内存: 4GB RAM
  存储: 50GB SSD
  网络: 10Mbps带宽

推荐配置 (生产环境):
  CPU: 4核心 (支持自动扩展到8核)
  内存: 8GB RAM (支持扩展到16GB)
  存储: 100GB SSD (数据盘另计)
  网络: 100Mbps带宽
  
生产集群配置:
  Web服务器: 2-4台 (负载均衡)
  数据库服务器: 1台主 + 1台从
  缓存服务器: 1台Redis集群
  监控服务器: 1台
```

### 软件要求
```yaml
操作系统:
  - Ubuntu 22.04 LTS (推荐)
  - CentOS 8+ 
  - Docker支持

运行时环境:
  - Python 3.11+
  - Node.js 18+ (可选，前端构建)
  - Docker 20.10+
  - Docker Compose 2.0+

数据库:
  - PostgreSQL 15+
  - Redis 7+
  - MongoDB 6+ (可选，日志存储)

网络组件:
  - Nginx 1.20+ (反向代理)
  - Certbot (SSL证书)
```

## 环境准备

### 1. 服务器初始化
```bash
#!/bin/bash
# 服务器初始化脚本

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y curl wget git vim htop net-tools

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 创建项目目录
sudo mkdir -p /opt/github-auto-register
sudo chown $USER:$USER /opt/github-auto-register
cd /opt/github-auto-register

echo "服务器初始化完成!"
```

### 2. 防火墙配置
```bash
# 配置UFW防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8000/tcp  # API服务端口
sudo ufw reload

# 查看防火墙状态
sudo ufw status
```

### 3. SSL证书配置
```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d api.github-auto-register.com
sudo certbot --nginx -d www.github-auto-register.com

# 设置自动续期
sudo crontab -e
# 添加以下行:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 数据库部署

### 1. PostgreSQL主数据库
```yaml
# docker-compose.yml - 数据库服务
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: github-auto-postgres
    environment:
      POSTGRES_DB: github_auto_register
      POSTGRES_USER: github_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB
      -c random_page_cost=1.1
      -c temp_file_limit=2GB
      -c log_min_duration_statement=1000
      -c log_connections=on
      -c log_disconnections=on

volumes:
  postgres_data:
    driver: local
```

### 2. Redis缓存服务
```yaml
# Redis配置
  redis:
    image: redis:7-alpine
    container_name: github-auto-redis
    command: >
      redis-server 
      --appendonly yes
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --timeout 300
      --tcp-keepalive 60
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/etc/redis/redis.conf
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
```

### 3. 数据库初始化
```sql
-- sql/init.sql
-- 数据库初始化脚本

-- 创建用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    profile JSONB DEFAULT '{}'
);

-- 创建许可证表
CREATE TABLE licenses (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    license_key VARCHAR(64) UNIQUE NOT NULL,
    plan_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    usage_count INTEGER DEFAULT 0,
    usage_limit INTEGER NOT NULL,
    device_fingerprint TEXT,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}'
);

-- 创建账号生成记录表
CREATE TABLE generated_accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(64) NOT NULL,
    email VARCHAR(255) NOT NULL,
    username VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    generation_algorithm VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    success_status VARCHAR(50) DEFAULT 'pending',
    metadata JSONB DEFAULT '{}'
);

-- 创建使用日志表
CREATE TABLE usage_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    license_id INTEGER REFERENCES licenses(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    request_details JSONB DEFAULT '{}',
    response_status INTEGER,
    processing_time INTEGER
);

-- 创建系统配置表
CREATE TABLE system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_licenses_user_id ON licenses(user_id);
CREATE INDEX idx_licenses_key ON licenses(license_key);
CREATE INDEX idx_generated_accounts_user_id ON generated_accounts(user_id);
CREATE INDEX idx_generated_accounts_session ON generated_accounts(session_id);
CREATE INDEX idx_usage_logs_user_id_timestamp ON usage_logs(user_id, timestamp DESC);
CREATE INDEX idx_usage_logs_timestamp ON usage_logs(timestamp DESC);

-- 插入初始配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('maintenance_mode', 'false', '维护模式开关'),
('max_daily_generations', '1000', '每日最大生成数量'),
('default_rate_limit', '50', '默认请求频率限制'),
('email_verification_required', 'true', '是否需要邮箱验证');

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 后端服务部署

### 1. 应用配置文件
```python
# config/settings.py
import os
from typing import List
from pydantic import BaseSettings

class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "GitHub Auto Register API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL", 
        "***********************************************/github_auto_register"
    )
    
    # Redis配置
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://redis:6379/0")
    
    # JWT配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 24小时
    
    # API配置
    API_V1_STR: str = "/v1"
    CORS_ORIGINS: List[str] = ["*"]
    
    # 外部服务配置
    TEMPMAIL_API_KEY: str = os.getenv("TEMPMAIL_API_KEY")
    TEMPMAIL_BASE_URL: str = "https://tempmail.plus/api"
    
    # 限流配置
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_PER_HOUR: int = 1000
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 安全配置
    ALLOWED_HOSTS: List[str] = ["*"]
    SECURE_HEADERS: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### 2. Docker镜像构建
```dockerfile
# Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非特权用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

### 3. 应用服务配置
```yaml
# docker-compose.yml - 应用服务
  web:
    build: .
    container_name: github-auto-web
    environment:
      - DATABASE_URL=postgresql://github_user:${POSTGRES_PASSWORD}@postgres:5432/github_auto_register
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - TEMPMAIL_API_KEY=${TEMPMAIL_API_KEY}
      - DEBUG=false
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # 后台任务处理器
  worker:
    build: .
    container_name: github-auto-worker
    command: celery -A app.worker worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=postgresql://github_user:${POSTGRES_PASSWORD}@postgres:5432/github_auto_register
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - TEMPMAIL_API_KEY=${TEMPMAIL_API_KEY}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # 定时任务调度器
  beat:
    build: .
    container_name: github-auto-beat
    command: celery -A app.worker beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://github_user:${POSTGRES_PASSWORD}@postgres:5432/github_auto_register
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
```

### 4. 环境变量配置
```bash
# .env 文件
# 数据库配置
POSTGRES_PASSWORD=your_strong_password_here
DATABASE_URL=****************************************************************/github_auto_register

# 应用配置
SECRET_KEY=your_jwt_secret_key_here_minimum_32_characters
DEBUG=false
APP_ENV=production

# 外部服务
TEMPMAIL_API_KEY=your_tempmail_api_key
EMAIL_SERVICE_API_KEY=your_email_service_key

# Redis配置
REDIS_URL=redis://redis:6379/0

# 监控配置
SENTRY_DSN=your_sentry_dsn_here
PROMETHEUS_ENABLED=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log
```

## 反向代理配置

### 1. Nginx配置
```nginx
# /etc/nginx/sites-available/github-auto-register
upstream github_auto_backend {
    least_conn;
    server 127.0.0.1:8000 max_fails=3 fail_timeout=30s;
    # 如果有多个实例，添加更多server行
    # server 127.0.0.1:8001 max_fails=3 fail_timeout=30s;
}

# HTTP到HTTPS重定向
server {
    listen 80;
    server_name api.github-auto-register.com www.github-auto-register.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS主配置
server {
    listen 443 ssl http2;
    server_name api.github-auto-register.com;

    # SSL配置
    ssl_certificate /etc/letsencrypt/live/api.github-auto-register.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.github-auto-register.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'";

    # 请求大小限制
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # 日志配置
    access_log /var/log/nginx/github-auto-register.access.log;
    error_log /var/log/nginx/github-auto-register.error.log;

    # API路由
    location /v1/ {
        # 限流配置
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;

        # 代理配置
        proxy_pass http://github_auto_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 健康检查
    location /health {
        proxy_pass http://github_auto_backend/health;
        access_log off;
    }

    # 静态文件
    location /static/ {
        alias /opt/github-auto-register/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 文档页面
    location /docs {
        proxy_pass http://github_auto_backend/docs;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 限流配置
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
}
```

### 2. Nginx优化配置
```nginx
# /etc/nginx/nginx.conf 主配置优化
user www-data;
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
}

http {
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 缓冲区配置
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
```

## 监控与日志

### 1. Prometheus监控
```yaml
# docker-compose.yml - 监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: github-auto-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: github-auto-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  prometheus_data:
  grafana_data:
```

### 2. Prometheus配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'github-auto-register'
    static_configs:
      - targets: ['web:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 3. 日志收集配置
```yaml
# docker-compose.yml - 日志服务
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: github-auto-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    restart: unless-stopped

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: github-auto-logstash
    volumes:
      - ./monitoring/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./logs:/usr/share/logstash/logs
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch
    restart: unless-stopped

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: github-auto-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    restart: unless-stopped

volumes:
  elasticsearch_data:
```

## 部署脚本

### 1. 一键部署脚本
```bash
#!/bin/bash
# deploy.sh - 一键部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# 检查依赖
check_dependencies() {
    log "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker 未安装，请先安装 Docker"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose 未安装，请先安装 Docker Compose"
    fi
    
    if ! command -v nginx &> /dev/null; then
        warn "Nginx 未安装，将跳过反向代理配置"
    fi
    
    log "依赖检查完成"
}

# 环境配置
setup_environment() {
    log "配置环境变量..."
    
    if [ ! -f .env ]; then
        log "创建 .env 文件..."
        cat > .env << EOF
# 数据库配置
POSTGRES_PASSWORD=$(openssl rand -base64 32)
DATABASE_URL=postgresql://github_user:\${POSTGRES_PASSWORD}@postgres:5432/github_auto_register

# 应用配置
SECRET_KEY=$(openssl rand -base64 64)
DEBUG=false
APP_ENV=production

# 外部服务（需要手动配置）
TEMPMAIL_API_KEY=your_tempmail_api_key
EMAIL_SERVICE_API_KEY=your_email_service_key

# Redis配置
REDIS_URL=redis://redis:6379/0

# 监控配置
GRAFANA_PASSWORD=$(openssl rand -base64 16)
EOF
        log "请编辑 .env 文件，配置外部服务的API密钥"
        read -p "配置完成后按 Enter 继续..."
    fi
    
    log "环境配置完成"
}

# 构建和启动服务
deploy_services() {
    log "构建Docker镜像..."
    docker-compose build

    log "启动数据库服务..."
    docker-compose up -d postgres redis

    log "等待数据库启动..."
    sleep 30

    log "运行数据库迁移..."
    docker-compose run --rm web python -m alembic upgrade head

    log "启动应用服务..."
    docker-compose up -d web worker beat

    log "启动监控服务..."
    docker-compose up -d prometheus grafana

    log "服务部署完成"
}

# 配置Nginx
setup_nginx() {
    if command -v nginx &> /dev/null; then
        log "配置Nginx反向代理..."
        
        # 备份现有配置
        if [ -f /etc/nginx/sites-available/github-auto-register ]; then
            sudo cp /etc/nginx/sites-available/github-auto-register /etc/nginx/sites-available/github-auto-register.backup
        fi
        
        # 复制配置文件
        sudo cp nginx/github-auto-register /etc/nginx/sites-available/
        sudo ln -sf /etc/nginx/sites-available/github-auto-register /etc/nginx/sites-enabled/
        
        # 测试配置
        sudo nginx -t && sudo systemctl reload nginx
        
        log "Nginx配置完成"
    else
        warn "跳过Nginx配置"
    fi
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    # 检查服务状态
    sleep 10
    
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log "应用服务健康检查通过"
    else
        error "应用服务健康检查失败"
    fi
    
    if curl -f http://localhost:9090/-/healthy > /dev/null 2>&1; then
        log "Prometheus服务健康检查通过"
    else
        warn "Prometheus服务健康检查失败"
    fi
    
    log "健康检查完成"
}

# 显示部署信息
show_deployment_info() {
    log "部署完成！"
    echo ""
    echo "服务访问信息："
    echo "- API服务: http://localhost:8000"
    echo "- API文档: http://localhost:8000/docs"
    echo "- Prometheus: http://localhost:9090"
    echo "- Grafana: http://localhost:3000 (admin/$(grep GRAFANA_PASSWORD .env | cut -d'=' -f2))"
    echo ""
    echo "数据库信息："
    echo "- PostgreSQL: localhost:5432"
    echo "- Redis: localhost:6379"
    echo ""    
    echo "日志查看："
    echo "- 应用日志: docker-compose logs -f web"
    echo "- 数据库日志: docker-compose logs -f postgres"
    echo ""
    echo "管理命令："
    echo "- 停止服务: docker-compose down"
    echo "- 重启服务: docker-compose restart"
    echo "- 查看状态: docker-compose ps"
}

# 主函数
main() {
    log "开始部署 GitHub自动注册服务..."
    
    check_dependencies
    setup_environment
    deploy_services
    setup_nginx
    health_check
    show_deployment_info
    
    log "部署完成！"
}

# 错误处理
trap 'error "部署过程中发生错误，请检查日志"' ERR

# 执行主函数
main "$@"
```

### 2. 更新脚本
```bash
#!/bin/bash
# update.sh - 服务更新脚本

set -e

log() {
    echo -e "\033[0;32m[$(date +'%Y-%m-%d %H:%M:%S')] $1\033[0m"
}

log "开始更新服务..."

# 备份数据库
log "备份数据库..."
docker-compose exec postgres pg_dump -U github_user github_auto_register > backup_$(date +%Y%m%d_%H%M%S).sql

# 拉取最新代码
log "拉取最新代码..."
git pull origin main

# 重新构建镜像
log "重新构建Docker镜像..."
docker-compose build --no-cache

# 停止旧服务
log "停止旧服务..."
docker-compose down

# 运行数据库迁移
log "运行数据库迁移..."
docker-compose run --rm web python -m alembic upgrade head

# 启动新服务
log "启动新服务..."
docker-compose up -d

# 健康检查
log "等待服务启动..."
sleep 30

if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    log "服务更新成功！"
else
    log "服务更新失败，请检查日志"
    exit 1
fi
```

### 3. 备份脚本
```bash
#!/bin/bash
# backup.sh - 数据备份脚本

BACKUP_DIR="/opt/backups/github-auto-register"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
docker-compose exec -T postgres pg_dump -U github_user github_auto_register | gzip > $BACKUP_DIR/database_$DATE.sql.gz

# 备份Redis数据
docker-compose exec -T redis redis-cli --rdb - | gzip > $BACKUP_DIR/redis_$DATE.rdb.gz

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz .env docker-compose.yml nginx/

# 清理7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR"
```

## 客户端脚本部署

### 1. 脚本打包和分发
```bash
#!/bin/bash
# package-client.sh - 客户端脚本打包

VERSION="3.8"
BUILD_DIR="dist"
SCRIPT_NAME="github-auto-register-client.user.js"

mkdir -p $BUILD_DIR

# 更新版本号和API端点
sed -e "s/{{VERSION}}/$VERSION/g" \
    -e "s/{{API_BASE}}/https:\/\/api.github-auto-register.com\/v1/g" \
    client/template.js > $BUILD_DIR/$SCRIPT_NAME

# 生成校验和
sha256sum $BUILD_DIR/$SCRIPT_NAME > $BUILD_DIR/$SCRIPT_NAME.sha256

# 创建发布包
tar -czf $BUILD_DIR/github-auto-register-v$VERSION.tar.gz -C $BUILD_DIR $SCRIPT_NAME $SCRIPT_NAME.sha256

echo "客户端脚本打包完成: $BUILD_DIR/github-auto-register-v$VERSION.tar.gz"
```

### 2. 自动更新服务
```python
# 客户端更新检查API
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter()

class UpdateCheckResponse(BaseModel):
    current_version: str
    latest_version: str
    update_available: bool
    download_url: str
    changelog: str

@router.get("/client/update-check")
async def check_client_update(current_version: str):
    """检查客户端更新"""
    LATEST_VERSION = "3.8"
    
    if current_version != LATEST_VERSION:
        return UpdateCheckResponse(
            current_version=current_version,
            latest_version=LATEST_VERSION,
            update_available=True,
            download_url=f"https://api.github-auto-register.com/client/download/{LATEST_VERSION}",
            changelog="修复GitHub用户名规则适配，优化账号生成算法"
        )
    
    return UpdateCheckResponse(
        current_version=current_version,
        latest_version=LATEST_VERSION,
        update_available=False,
        download_url="",
        changelog=""
    )

@router.get("/client/download/{version}")
async def download_client(version: str):
    """下载客户端脚本"""
    # 实现文件下载逻辑
    pass
```

## 安全配置

### 1. 防火墙规则
```bash
#!/bin/bash
# security.sh - 安全配置脚本

# 配置UFW防火墙
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许必要端口
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 限制特定端口只允许本地访问
sudo ufw allow from 127.0.0.1 to any port 5432  # PostgreSQL
sudo ufw allow from 127.0.0.1 to any port 6379  # Redis
sudo ufw allow from 127.0.0.1 to any port 9090  # Prometheus
sudo ufw allow from 127.0.0.1 to any port 3000  # Grafana

# 启用防火墙
sudo ufw --force enable

# 配置fail2ban
sudo apt install -y fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# 配置SSH安全
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

echo "安全配置完成"
```

### 2. SSL证书自动续期
```bash
#!/bin/bash
# ssl-renew.sh - SSL证书续期脚本

# 续期证书
certbot renew --quiet

# 重载Nginx配置
if [ $? -eq 0 ]; then
    systemctl reload nginx
    echo "SSL证书续期成功"
else
    echo "SSL证书续期失败" >&2
    exit 1
fi
```

## 监控告警配置

### 1. 告警规则
```yaml
# monitoring/rules/alerts.yml
groups:
  - name: github-auto-register
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.instance }} has been down for more than 5 minutes."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.instance }}"
          description: "Error rate is {{ $value }} per second"

      - alert: DatabaseConnectionHigh
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "Database has {{ $value }} active connections"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is at {{ $value }}%"
```

这份部署指南提供了完整的生产环境部署方案，包括高可用性配置、安全加固、监控告警等关键组件，确保服务的稳定运行和可维护性。