# GitHub自动注册服务 - API接口文档

## API概览

### 基本信息
- **API版本**: v1.0
- **基础URL**: `https://api.github-auto-register.com/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 全局配置
```yaml
环境配置:
  生产环境: https://api.github-auto-register.com/v1
  测试环境: https://api-test.github-auto-register.com/v1
  开发环境: http://localhost:8000/v1

请求头要求:
  Content-Type: application/json
  Authorization: Bearer {access_token}
  X-API-Version: v1
  User-Agent: GitHubAutoRegister/1.0

响应格式:
  成功: HTTP 200/201 + JSON数据
  错误: HTTP 4xx/5xx + 错误信息
```

## 认证授权接口

### 1. 用户注册
**POST** `/auth/register`

**请求参数**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123",
  "username": "developer001",
  "referral_code": "REF123456" // 可选
}
```

**响应示例**
```json
{
  "success": true,
  "message": "用户注册成功",
  "data": {
    "user_id": 12345,
    "email": "<EMAIL>",
    "username": "developer001",
    "created_at": "2024-01-15T10:30:00Z",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400
  }
}
```

**错误响应**
```json
{
  "success": false,
  "error": {
    "code": "EMAIL_ALREADY_EXISTS",
    "message": "该邮箱已被注册",
    "details": "请使用其他邮箱地址"
  }
}
```

### 2. 用户登录
**POST** `/auth/login`

**请求参数**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123"
}
```

**响应示例**
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user_id": 12345,
    "email": "<EMAIL>",
    "username": "developer001",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400,
    "last_login": "2024-01-15T10:30:00Z"
  }
}
```

### 3. Token刷新
**POST** `/auth/refresh`

**请求参数**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400
  }
}
```

### 4. 用户信息查询
**GET** `/auth/profile`

**请求头**
```
Authorization: Bearer {access_token}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "user_id": 12345,
    "email": "<EMAIL>",
    "username": "developer001",
    "plan_type": "premium",
    "created_at": "2024-01-15T10:30:00Z",
    "last_login": "2024-01-20T15:45:00Z",
    "profile": {
      "avatar_url": "https://example.com/avatar.jpg",
      "bio": "Full Stack Developer",
      "location": "Beijing, China"
    }
  }
}
```

## 许可证管理接口

### 5. 许可证验证
**POST** `/license/validate`

**请求参数**
```json
{
  "license_key": "LIC-ABCD1234-EFGH5678-IJKL9012",
  "device_fingerprint": "fp_1234567890abcdef",
  "action_type": "account_generation"
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "license_info": {
      "license_key": "LIC-ABCD1234-EFGH5678-IJKL9012",
      "plan_type": "premium",
      "expires_at": "2024-04-15T23:59:59Z",
      "features": ["account_generation", "batch_operations", "priority_support"]
    },
    "usage_info": {
      "current_usage": 45,
      "usage_limit": 500,
      "remaining_quota": 455,
      "reset_date": "2024-02-01T00:00:00Z"
    }
  }
}
```

### 6. 许可证使用统计
**GET** `/license/quota`

**请求头**
```
Authorization: Bearer {access_token}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "current_period": {
      "start_date": "2024-01-01T00:00:00Z",
      "end_date": "2024-01-31T23:59:59Z",
      "total_quota": 500,
      "used_quota": 45,
      "remaining_quota": 455
    },
    "usage_history": [
      {
        "date": "2024-01-20",
        "count": 12,
        "success_rate": 0.92
      },
      {
        "date": "2024-01-19", 
        "count": 8,
        "success_rate": 0.88
      }
    ],
    "features_available": [
      "account_generation",
      "batch_operations", 
      "priority_support",
      "api_access"
    ]
  }
}
```

## 账号生成接口

### 7. 生成GitHub账号
**POST** `/accounts/generate`

**请求参数**
```json
{
  "license_key": "LIC-ABCD1234-EFGH5678-IJKL9012",
  "device_fingerprint": "fp_1234567890abcdef",
  "generation_options": {
    "account_type": "standard",
    "email_domain": "neoz.ltd",
    "username_style": "meaningful_words",
    "custom_preferences": {
      "language": "chinese_pinyin",
      "include_numbers": true,
      "min_length": 6,
      "max_length": 20
    }
  }
}
```

**响应示例**
```json
{
  "success": true,
  "message": "账号生成成功",
  "data": {
    "session_id": "sess_1234567890abcdef",
    "account_info": {
      "email": "<EMAIL>",
      "username": "xiaoming-dev-2024",
      "password": "Kj9mN2pQ7xR5wT8v",
      "generated_at": "2024-01-20T16:30:00Z"
    },
    "generation_details": {
      "algorithm_version": "v3.8",
      "style_used": "chinese_word_number",
      "domain_selected": "@neoz.ltd",
      "success_probability": 0.89
    },
    "remaining_quota": 454
  }
}
```

### 8. 批量生成账号
**POST** `/accounts/batch-generate`

**请求参数**
```json
{
  "license_key": "LIC-ABCD1234-EFGH5678-IJKL9012",
  "device_fingerprint": "fp_1234567890abcdef",
  "batch_config": {
    "count": 10,
    "account_type": "standard",
    "generation_options": {
      "email_domains": ["neoz.ltd", "zhou1.shop"],
      "username_styles": ["meaningful_words", "tech_terms"],
      "ensure_unique": true
    }
  }
}
```

**响应示例**
```json
{
  "success": true,
  "message": "批量生成完成",
  "data": {
    "batch_id": "batch_1234567890",
    "total_requested": 10,
    "total_generated": 10,
    "accounts": [
      {
        "email": "<EMAIL>",
        "username": "david-pro-123", 
        "password": "Mq8nB4tY6zX9kR2s",
        "success_probability": 0.91
      },
      {
        "email": "<EMAIL>",
        "username": "javascript-dev-456",
        "password": "Lp7wC3vH5yU8jN6m",
        "success_probability": 0.87
      }
    ],
    "generation_summary": {
      "start_time": "2024-01-20T16:30:00Z",
      "end_time": "2024-01-20T16:30:15Z",
      "average_success_probability": 0.89,
      "algorithms_used": ["chinese_pinyin", "tech_terms", "english_common"]
    },
    "remaining_quota": 445
  }
}
```

## 验证码服务接口

### 9. 请求验证码
**POST** `/verification/request`

**请求参数**
```json
{
  "session_id": "sess_1234567890abcdef",
  "email": "<EMAIL>",
  "verification_type": "github_signup",
  "max_wait_time": 300
}
```

**响应示例**
```json
{
  "success": true,
  "message": "验证码获取成功",
  "data": {
    "verification_code": "123456",
    "code_type": "numeric_6_digit",
    "retrieved_at": "2024-01-20T16:35:00Z",
    "email_subject": "GitHub Email Verification",
    "attempt_count": 1,
    "next_retry_after": 60
  }
}
```

### 10. 验证码获取状态
**GET** `/verification/status/{session_id}`

**响应示例**
```json
{
  "success": true,
  "data": {
    "session_id": "sess_1234567890abcdef",
    "status": "completed",
    "verification_code": "123456",
    "attempts": [
      {
        "attempt_number": 1,
        "timestamp": "2024-01-20T16:35:00Z",
        "result": "success",
        "email_found": true,
        "code_extracted": true
      }
    ],
    "total_wait_time": 45
  }
}
```

## 数据分析接口

### 11. 用户使用统计
**GET** `/analytics/usage`

**查询参数**
```
?start_date=2024-01-01&end_date=2024-01-31&granularity=daily
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_generations": 156,
      "successful_generations": 142,
      "success_rate": 0.91,
      "average_daily_usage": 5.2,
      "most_active_day": "2024-01-15"
    },
    "daily_breakdown": [
      {
        "date": "2024-01-20",
        "generations": 12,
        "successes": 11,
        "success_rate": 0.92,
        "popular_styles": ["chinese_pinyin", "tech_terms"]
      },
      {
        "date": "2024-01-19",
        "generations": 8,
        "successes": 7,
        "success_rate": 0.88,
        "popular_styles": ["english_common", "meaningful_words"]
      }
    ],
    "feature_usage": {
      "batch_generation": 15,
      "custom_preferences": 45,
      "api_calls": 89
    }
  }
}
```

### 12. 系统健康状态
**GET** `/system/health`

**响应示例**
```json
{
  "success": true,
  "data": {
    "system_status": "healthy",
    "timestamp": "2024-01-20T16:40:00Z",
    "services": {
      "auth_service": {
        "status": "healthy",
        "response_time": 45,
        "uptime": "99.99%"
      },
      "account_generator": {
        "status": "healthy", 
        "response_time": 120,
        "success_rate": "91.2%"
      },
      "verification_service": {
        "status": "healthy",
        "response_time": 89,
        "queue_size": 12
      },
      "database": {
        "status": "healthy",
        "connections": 45,
        "query_time": 23
      }
    },
    "performance_metrics": {
      "total_requests_today": 1256,
      "average_response_time": 78,
      "error_rate": "0.8%",
      "active_users": 89
    }
  }
}
```

## 错误处理

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "人类可读的错误描述",
    "details": "详细的错误信息或解决建议",
    "timestamp": "2024-01-20T16:45:00Z",
    "request_id": "req_1234567890abcdef"
  }
}
```

### 常见错误码
```yaml
认证相关:
  INVALID_TOKEN: "Token无效或已过期"
  INSUFFICIENT_PERMISSIONS: "权限不足"
  ACCOUNT_SUSPENDED: "账号已被暂停"

许可证相关:
  LICENSE_EXPIRED: "许可证已过期"
  LICENSE_INVALID: "许可证无效"
  QUOTA_EXCEEDED: "使用配额已超限"
  DEVICE_MISMATCH: "设备指纹不匹配"

业务逻辑:
  GENERATION_FAILED: "账号生成失败"
  VERIFICATION_TIMEOUT: "验证码获取超时"
  RATE_LIMIT_EXCEEDED: "请求频率超限"
  SERVICE_UNAVAILABLE: "服务暂时不可用"

系统错误:
  INTERNAL_SERVER_ERROR: "内部服务器错误"
  DATABASE_ERROR: "数据库连接错误"
  EXTERNAL_API_ERROR: "外部API调用失败"
```

### HTTP状态码说明
```yaml
2xx 成功:
  200: 请求成功
  201: 资源创建成功
  202: 请求已接受，异步处理中

4xx 客户端错误:
  400: 请求参数错误
  401: 未授权访问
  403: 权限不足
  404: 资源不存在
  409: 资源冲突
  429: 请求频率限制

5xx 服务端错误:
  500: 内部服务器错误
  502: 网关错误
  503: 服务不可用
  504: 网关超时
```

## 限流和配额

### 请求限制
```yaml
免费版用户:
  - 每小时最多50个请求
  - 每天最多200个请求
  - 并发连接数限制为5

付费版用户:
  - 基础版: 每小时200个请求
  - 专业版: 每小时500个请求
  - 企业版: 无请求频率限制

特殊限制:
  - 批量操作: 单次最多100个账号
  - 验证码请求: 每个邮箱每小时最多10次
  - 登录尝试: 每个IP每小时最多20次
```

### 配额管理
```yaml
配额计算:
  - 成功生成账号: 消耗1配额
  - 失败但尝试生成: 消耗0.1配额
  - 验证码获取: 消耗0.5配额
  - 批量操作: 按实际生成数量计算

配额重置:
  - 月度计划: 每月1号00:00重置
  - 年度计划: 每年购买日重置
  - 按需付费: 实时扣减，无重置

超额处理:
  - 自动停用超额功能
  - 提供升级建议
  - 允许购买额外配额
```

## SDK和集成

### 官方SDK
```yaml
支持语言:
  - JavaScript/Node.js
  - Python
  - Java
  - Go
  - PHP

安装方式:
  JavaScript: npm install github-auto-register-sdk
  Python: pip install github-auto-register
  Java: Maven/Gradle依赖
```

### JavaScript SDK示例
```javascript
const GitHubAutoRegister = require('github-auto-register-sdk');

const client = new GitHubAutoRegister({
  apiKey: 'your-api-key',
  baseURL: 'https://api.github-auto-register.com/v1'
});

// 生成账号
async function generateAccount() {
  try {
    const result = await client.accounts.generate({
      accountType: 'standard',
      emailDomain: 'neoz.ltd',
      usernameStyle: 'meaningful_words'
    });
    
    console.log('生成成功:', result.data.account_info);
  } catch (error) {
    console.error('生成失败:', error.message);
  }
}

// 验证许可证
async function validateLicense() {
  try {
    const result = await client.license.validate({
      licenseKey: 'LIC-ABCD1234-EFGH5678-IJKL9012',
      deviceFingerprint: 'fp_1234567890abcdef'
    });
    
    console.log('许可证状态:', result.data);
  } catch (error) {
    console.error('验证失败:', error.message);
  }
}
```

### Webhook回调
```yaml
支持事件:
  - account.generated: 账号生成完成
  - verification.completed: 验证码获取完成
  - quota.warning: 配额不足警告
  - license.expired: 许可证过期提醒

回调格式:
  POST到用户指定的webhook URL
  包含事件类型和相关数据
  支持签名验证确保安全性
```

## 最佳实践

### 性能优化建议
```yaml
批量操作:
  - 使用批量接口而非循环调用
  - 合理设置批量大小(建议10-50个)
  - 考虑异步处理大批量任务

缓存策略:
  - 缓存用户信息和许可证状态
  - 本地存储生成的账号信息
  - 避免频繁调用验证接口

错误处理:
  - 实现指数退避重试机制
  - 处理网络超时和临时故障
  - 记录详细的错误日志

安全性:
  - 安全存储API密钥
  - 使用HTTPS进行所有通信
  - 定期轮换访问凭证
  - 监控异常API调用模式
```

### 集成指南
```yaml
开发环境:
  1. 注册开发者账号
  2. 获取测试API密钥
  3. 使用测试环境进行开发
  4. 参考示例代码和SDK

生产部署:
  1. 申请生产环境密钥
  2. 配置错误监控和日志
  3. 设置webhook回调
  4. 进行压力测试和验证

监控维护:
  1. 监控API调用成功率
  2. 跟踪配额使用情况
  3. 设置告警阈值
  4. 定期更新SDK版本
```

这份API文档提供了完整的接口规范和使用指南，帮助开发者快速集成GitHub自动注册服务。如需更多技术支持，请联系我们的技术团队。